<?xml version="1.0" encoding="UTF-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1">
    <asset>
        <contributor>
            <author>VCGLab</author>
            <authoring_tool>VCGLib | MeshLab</authoring_tool>
        </contributor>
        <created>Wed Aug 22 18:54:08 2018 GMT</created>
        <modified>Wed Aug 22 18:54:08 2018 GMT</modified>
        <up_axis>Y_UP</up_axis>
    </asset>
    <library_geometries>
        <geometry id="shape0-lib" name="shape0">
            <mesh>
                <source id="shape0-lib-positions" name="position">
                    <float_array id="shape0-lib-positions-array" count="147">-0.0068518 0 0.0202589 -0.0059339 -0.00342592 0.0202589 -0.0118677 0 0.0152431 -0.0102777 -0.00593378 0.0152431 -0.0137036 0 0.0083912 -0.0118677 -0.00685184 0.0083912 -0.0179977 -0.010391 0.0036913 -0.0179977 0.010391 0.0036913 -0.0179977 -0.010391 4.45e-05 -0.0179977 0.010391 4.45e-05 0 0.00685184 0.0202589 -0.0034259 0.00593388 0.0202589 -0.0059338 0.00342592 0.0202589 0 0.0118677 0.0152431 -0.0059338 0.0102777 0.0152431 -0.0102777 0.00593388 0.0152431 0 0.0137036 0.0083912 -0.0068518 0.0118677 0.0083912 -0.0118677 0.00685184 0.0083912 -0.0034259 -0.00593388 0.0202589 -0.0059339 -0.0102777 0.0152431 0 -0.0118677 0.0152431 -0.0068518 -0.0118677 0.0083912 0 -0.00685184 0.0202589 0.0034259 -0.00593388 0.0202589 0.0059338 -0.00342592 0.0202589 0.0059338 -0.0102777 0.0152431 0 -0.0137036 0.0083912 0.0068518 -0.0118677 0.0083912 0 -0.020782 0.0036913 0 -0.020782 4.45e-05 0.0068518 0 0.0202589 0.0102777 -0.00593388 0.0152431 0.0118677 0 0.0152431 0.0118677 -0.00685184 0.0083912 0.0137036 0 0.0083912 0.0179977 -0.010391 0.0036913 0.0179977 -0.010391 4.45e-05 0 0 0.0220949 0.0059339 0.00342592 0.0202589 0.0034259 0.00593388 0.0202589 0.0102777 0.00593378 0.0152431 0.0059339 0.0102777 0.0152431 0.0118677 0.00685184 0.0083912 0.0068518 0.0118677 0.0083912 0 0.020782 0.0036913 0.0179977 0.010391 0.0036913 0 0.020782 4.45e-05 0.0179977 0.010391 4.45e-05</float_array>
                    <technique_common>
                        <accessor count="49" source="#shape0-lib-positions-array" stride="3">
                            <param name="X" type="float"/>
                            <param name="Y" type="float"/>
                            <param name="Z" type="float"/>
                        </accessor>
                    </technique_common>
                </source>
                <source id="shape0-lib-normals" name="normal">
                    <float_array id="shape0-lib-normals-array" count="180">-0.258207 0.0691885 0.963609 -0.258207 -0.069181 0.963609 -0.694739 0.18616 0.694753 -0.694739 0.186157 0.694754 -0.694754 -0.186144 0.694743 -0.694739 -0.18616 0.694753 -0.935114 0.250567 0.250555 -0.935115 0.250557 0.250563 -0.93511 -0.25057 0.250567 -0.935117 -0.250558 0.250555 -0.0691854 0.258206 0.963609 -0.189023 0.189018 0.963609 -0.186156 0.694749 0.694744 -0.186162 0.694744 0.694747 -0.50859 0.508578 0.694756 -0.508583 0.508597 0.694746 -0.250569 0.935111 0.250566 -0.250561 0.935115 0.250557 -0.684541 0.68456 0.250561 -0.684544 0.684555 0.250566 -0.189018 -0.189021 0.96361 -0.0691854 -0.258206 0.963609 -0.508583 -0.508591 0.694751 -0.508596 -0.508586 0.694745 -0.186155 -0.694745 0.694748 -0.186159 -0.694746 0.694746 -0.684558 -0.684545 0.250556 -0.684544 -0.684554 0.250571 -0.250566 -0.935114 0.250557 -0.250561 -0.935114 0.250562 0.0691854 -0.258206 0.963609 0.189023 -0.189018 0.963609 0.186156 -0.694749 0.694744 0.186162 -0.694744 0.694747 0.50859 -0.508578 0.694756 0.508583 -0.508597 0.694746 0.250569 -0.935111 0.250566 0.250561 -0.935115 0.250557 0.684541 -0.68456 0.250561 0.684544 -0.684555 0.250566 0.258207 -0.0691885 0.963609 0.258207 0.069181 0.963609 0.694739 -0.18616 0.694753 0.694739 -0.186157 0.694754 0.694754 0.186144 0.694743 0.694739 0.18616 0.694753 0.935114 -0.250567 0.250555 0.935115 -0.250557 0.250563 0.93511 0.25057 0.250567 0.935117 0.250558 0.250555 0.189018 0.189021 0.96361 0.0691854 0.258206 0.963609 0.508583 0.508591 0.694751 0.508596 0.508586 0.694745 0.186155 0.694745 0.694748 0.186159 0.694746 0.694746 0.684558 0.684545 0.250556 0.684544 0.684554 0.250571 0.250566 0.935114 0.250557 0.250561 0.935114 0.250562</float_array>
                    <technique_common>
                        <accessor count="60" source="#shape0-lib-normals-array" stride="3">
                            <param name="X" type="float"/>
                            <param name="Y" type="float"/>
                            <param name="Z" type="float"/>
                        </accessor>
                    </technique_common>
                </source>
                <source id="shape0-lib-map" name="map">
                    <float_array id="shape0-lib-map-array" count="360">0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0</float_array>
                    <technique_common>
                        <accessor count="180" source="#shape0-lib-map-array" stride="2">
                            <param name="U" type="float"/>
                            <param name="V" type="float"/>
                        </accessor>
                    </technique_common>
                </source>
                <vertices id="shape0-lib-vertices">
                    <input semantic="POSITION" source="#shape0-lib-positions"/>
                </vertices>
                <triangles count="60">
                    <input offset="0" semantic="VERTEX" source="#shape0-lib-vertices"/>
                    <input offset="1" semantic="NORMAL" source="#shape0-lib-normals"/>
                    <input offset="2" semantic="TEXCOORD" source="#shape0-lib-map"/>
                    <p>38 0 0 12 0 1 0 0 2 38 1 3 0 1 4 1 1 5 2 2 6 0 2 7 12 2 8 12 3 9 15 3 10 2 3 11 3 4 12 1 4 13 0 4 14 0 5 15 2 5 16 3 5 17 4 6 18 2 6 19 15 6 20 15 7 21 18 7 22 4 7 23 5 8 24 3 8 25 2 8 26 2 9 27 4 9 28 5 9 29 38 10 30 10 10 31 11 10 32 38 11 33 11 11 34 12 11 35 14 12 36 11 12 37 10 12 38 10 13 39 13 13 40 14 13 41 15 14 42 12 14 43 11 14 44 11 15 45 14 15 46 15 15 47 17 16 48 14 16 49 13 16 50 13 17 51 16 17 52 17 17 53 18 18 54 15 18 55 14 18 56 14 19 57 17 19 58 18 19 59 38 20 60 1 20 61 19 20 62 38 21 63 19 21 64 23 21 65 20 22 66 19 22 67 1 22 68 1 23 69 3 23 70 20 23 71 21 24 72 23 24 73 19 24 74 19 25 75 20 25 76 21 25 77 22 26 78 20 26 79 3 26 80 3 27 81 5 27 82 22 27 83 27 28 84 21 28 85 20 28 86 20 29 87 22 29 88 27 29 89 38 30 90 23 30 91 24 30 92 38 31 93 24 31 94 25 31 95 26 32 96 24 32 97 23 32 98 23 33 99 21 33 100 26 33 101 32 34 102 25 34 103 24 34 104 24 35 105 26 35 106 32 35 107 28 36 108 26 36 109 21 36 110 21 37 111 27 37 112 28 37 113 34 38 114 32 38 115 26 38 116 26 39 117 28 39 118 34 39 119 38 40 120 25 40 121 31 40 122 38 41 123 31 41 124 39 41 125 33 42 126 31 42 127 25 42 128 25 43 129 32 43 130 33 43 131 41 44 132 39 44 133 31 44 134 31 45 135 33 45 136 41 45 137 35 46 138 33 46 139 32 46 140 32 47 141 34 47 142 35 47 143 43 48 144 41 48 145 33 48 146 33 49 147 35 49 148 43 49 149 38 50 150 39 50 151 40 50 152 38 51 153 40 51 154 10 51 155 42 52 156 40 52 157 39 52 158 39 53 159 41 53 160 42 53 161 13 54 162 10 54 163 40 54 164 40 55 165 42 55 166 13 55 167 44 56 168 42 56 169 41 56 170 41 57 171 43 57 172 44 57 173 16 58 174 13 58 175 42 58 176 42 59 177 44 59 178 16 59 179</p>
                </triangles>
            </mesh>
        </geometry>
    </library_geometries>
    <library_visual_scenes>
        <visual_scene id="VisualSceneNode" name="VisualScene">
            <node id="node" name="node">
                <instance_geometry url="#shape0-lib"/>
            </node>
        </visual_scene>
    </library_visual_scenes>
    <scene>
        <instance_visual_scene url="#VisualSceneNode"/>
    </scene>
</COLLADA>
